# =================================
# SendMe Logistics Backend Configuration
# =================================

# Server Configuration
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/sendme_logistics
MONGODB_TEST_URI=mongodb://localhost:27017/sendme_logistics_test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-change-this-in-production
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d

# Encryption & Security
BCRYPT_SALT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key-here

# SMS Gateway Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
SMS_PROVIDER=twilio

# Alternative SMS Provider (for local providers)
# LOCAL_SMS_API_URL=https://api.localsms.com/send
# LOCAL_SMS_API_KEY=your_local_sms_api_key
# LOCAL_SMS_USERNAME=your_local_sms_username

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=SendMe Logistics

# Cloud Storage Configuration (Cloudinary)
# CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
# CLOUDINARY_API_KEY=your_cloudinary_api_key
# CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Alternative: AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=sendme-logistics-uploads

# Payment Gateway Configuration (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
PAYMENT_PROVIDER=stripe

# Alternative Payment Provider
# LOCAL_PAYMENT_API_URL=https://api.localpayment.com
# LOCAL_PAYMENT_API_KEY=your_local_payment_api_key
# LOCAL_PAYMENT_MERCHANT_ID=your_merchant_id

# Google Maps API Configuration
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_PLACES_API_KEY=your_google_places_api_key

# Firebase Configuration (for push notifications)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Social Authentication
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_WINDOW_MS=900000
LOGIN_RATE_LIMIT_MAX_ATTEMPTS=5
OTP_RATE_LIMIT_WINDOW_MS=300000
OTP_RATE_LIMIT_MAX_ATTEMPTS=3

# Business Configuration
DEFAULT_COUNTRY=US
DEFAULT_CURRENCY=USD
DEFAULT_LANGUAGE=en
PLATFORM_COMMISSION_PERCENTAGE=15
DRIVER_COMMISSION_PERCENTAGE=85

# Pricing Configuration
BASE_FARE=5.00
PER_KM_RATE=2.50
WAITING_CHARGE_PER_MINUTE=0.50
PEAK_HOUR_MULTIPLIER=1.5
CANCELLATION_FEE=2.00

# Booking Configuration
MAX_BOOKING_DISTANCE_KM=100
MIN_BOOKING_DISTANCE_KM=1
DRIVER_SEARCH_RADIUS_KM=10
BOOKING_TIMEOUT_MINUTES=15
AUTO_ASSIGN_TIMEOUT_MINUTES=5

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif
ALLOWED_DOCUMENT_TYPES=application/pdf,image/jpeg,image/png

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Analytics Configuration
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX-X
FIREBASE_ANALYTICS_ENABLED=true

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+**********
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+**********

# Webhook URLs
STRIPE_WEBHOOK_URL=/api/webhooks/stripe
PAYMENT_SUCCESS_WEBHOOK_URL=/api/webhooks/payment-success
PAYMENT_FAILURE_WEBHOOK_URL=/api/webhooks/payment-failure

# Development & Testing
ENABLE_SWAGGER_DOCS=true
ENABLE_CORS_ALL_ORIGINS=false
ENABLE_REQUEST_LOGGING=true
MOCK_SMS_ENABLED=false
MOCK_PAYMENT_ENABLED=false

# Monitoring & Health Checks
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
ERROR_REPORTING_ENABLED=true
