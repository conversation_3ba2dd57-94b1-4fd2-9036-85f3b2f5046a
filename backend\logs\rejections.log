{
  error: TypeError: logger.error is not a function
      at runSeeders (D:\MERN\Sendme\backend\src\seeders\index.js:37:12)
      at Object.<anonymous> (D:\MERN\Sendme\backend\src\seeders\index.js:334:5)
      at Module._compile (node:internal/modules/cjs/loader:1739:14)
      at Object..js (node:internal/modules/cjs/loader:1904:10)
      at Module.load (node:internal/modules/cjs/loader:1473:32)
      at Function._load (node:internal/modules/cjs/loader:1285:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)
      at node:internal/main/run_main_module:33:47,
  level: 'error',
  message: 'unhandledRejection: logger.error is not a function\n' +
    'TypeError: logger.error is not a function\n' +
    '    at runSeeders (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:37:12)\n' +
    '    at Object.<anonymous> (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:334:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1739:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1904:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1473:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1285:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)\n' +
    '    at node:internal/main/run_main_module:33:47',
  stack: 'TypeError: logger.error is not a function\n' +
    '    at runSeeders (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:37:12)\n' +
    '    at Object.<anonymous> (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:334:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1739:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1904:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1473:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1285:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)\n' +
    '    at node:internal/main/run_main_module:33:47',
  rejection: true,
  date: 'Fri Aug 08 2025 13:21:06 GMT+0530 (India Standard Time)',
  process: {
    pid: 17180,
    uid: null,
    gid: null,
    cwd: 'D:\\MERN\\Sendme\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v23.6.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js',
      'seed'
    ],
    memoryUsage: {
      rss: 68505600,
      heapTotal: 33906688,
      heapUsed: 20024896,
      external: 20381160,
      arrayBuffers: 18262288
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 6100.921 },
  trace: [
    {
      column: 12,
      file: 'D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js',
      function: 'runSeeders',
      line: 37,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js',
      function: null,
      line: 334,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1739,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1904,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1473,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1285,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 234,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'Function.executeUserEntryPoint [as runMain]',
      line: 151,
      method: 'executeUserEntryPoint [as runMain]',
      native: false
    },
    {
      column: 47,
      file: 'node:internal/main/run_main_module',
      function: null,
      line: 33,
      method: null,
      native: false
    }
  ],
  service: 'sendme-logistics-api',
  environment: 'development',
  timestamp: '2025-08-08 13:21:06'
}
{
  error: TypeError: logger.error is not a function
      at runSeeders (D:\MERN\Sendme\backend\src\seeders\index.js:37:12)
      at Object.<anonymous> (D:\MERN\Sendme\backend\src\seeders\index.js:334:5)
      at Module._compile (node:internal/modules/cjs/loader:1739:14)
      at Object..js (node:internal/modules/cjs/loader:1904:10)
      at Module.load (node:internal/modules/cjs/loader:1473:32)
      at Function._load (node:internal/modules/cjs/loader:1285:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)
      at node:internal/main/run_main_module:33:47,
  level: 'error',
  message: 'unhandledRejection: logger.error is not a function\n' +
    'TypeError: logger.error is not a function\n' +
    '    at runSeeders (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:37:12)\n' +
    '    at Object.<anonymous> (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:334:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1739:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1904:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1473:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1285:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)\n' +
    '    at node:internal/main/run_main_module:33:47',
  stack: 'TypeError: logger.error is not a function\n' +
    '    at runSeeders (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:37:12)\n' +
    '    at Object.<anonymous> (D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js:334:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1739:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1904:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1473:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1285:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)\n' +
    '    at node:internal/main/run_main_module:33:47',
  rejection: true,
  date: 'Fri Aug 08 2025 13:22:12 GMT+0530 (India Standard Time)',
  process: {
    pid: 17424,
    uid: null,
    gid: null,
    cwd: 'D:\\MERN\\Sendme\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v23.6.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js',
      'seed'
    ],
    memoryUsage: {
      rss: 68292608,
      heapTotal: 33906688,
      heapUsed: 19948656,
      external: 20381160,
      arrayBuffers: 18262288
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 6167.312 },
  trace: [
    {
      column: 12,
      file: 'D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js',
      function: 'runSeeders',
      line: 37,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'D:\\MERN\\Sendme\\backend\\src\\seeders\\index.js',
      function: null,
      line: 334,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1739,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1904,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1473,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1285,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 234,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'Function.executeUserEntryPoint [as runMain]',
      line: 151,
      method: 'executeUserEntryPoint [as runMain]',
      native: false
    },
    {
      column: 47,
      file: 'node:internal/main/run_main_module',
      function: null,
      line: 33,
      method: null,
      native: false
    }
  ],
  service: 'sendme-logistics-api',
  environment: 'development',
  timestamp: '2025-08-08 13:22:12'
}
