/**
 * Test Backend Structure
 * Simple test to verify all modules can be loaded without database connection
 */

const path = require('path');
const fs = require('fs');

console.log('🚀 Testing SendMe Logistics Backend Structure...\n');

// Test environment variables
console.log('📋 Environment Configuration:');
require('dotenv').config();
console.log(`✅ NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`✅ PORT: ${process.env.PORT}`);
console.log(`✅ JWT_SECRET: ${process.env.JWT_SECRET ? 'Set' : 'Not Set'}`);
console.log(`✅ MONGODB_URI: ${process.env.MONGODB_URI ? 'Set' : 'Not Set'}\n`);

// Test logger
console.log('📝 Testing Logger:');
try {
  const logger = require('./src/utils/logger');
  console.log('✅ Logger loaded successfully');
  logger.info('Test log message');
  console.log('✅ Logger.info() works');
  logger.error('Test error message');
  console.log('✅ Logger.error() works');
} catch (error) {
  console.log('❌ Logger failed:', error.message);
}

// Test utilities
console.log('\n🔧 Testing Utilities:');
const utilities = [
  'jwt',
  'email',
  'sms',
  'pricing',
  'distance'
];

utilities.forEach(util => {
  try {
    require(`./src/utils/${util}`);
    console.log(`✅ ${util}.js loaded successfully`);
  } catch (error) {
    console.log(`❌ ${util}.js failed:`, error.message);
  }
});

// Test middleware
console.log('\n🛡️ Testing Middleware:');
const middleware = [
  'errorHandler',
  'rateLimiter',
  'validation'
];

middleware.forEach(mw => {
  try {
    require(`./src/middleware/${mw}`);
    console.log(`✅ ${mw}.js loaded successfully`);
  } catch (error) {
    console.log(`❌ ${mw}.js failed:`, error.message);
  }
});

// Test models (without database connection)
console.log('\n📊 Testing Models:');
const models = [
  'User',
  'Booking',
  'Vehicle',
  'Review',
  'PromoCode',
  'Notification',
  'SupportTicket',
  'CmsContent'
];

models.forEach(model => {
  try {
    require(`./src/models/${model}`);
    console.log(`✅ ${model}.js loaded successfully`);
  } catch (error) {
    console.log(`❌ ${model}.js failed:`, error.message);
  }
});

// Test controllers
console.log('\n🎮 Testing Controllers:');
const controllers = [
  'authController',
  'customerController',
  'driverController',
  'adminController',
  'publicController'
];

controllers.forEach(controller => {
  try {
    require(`./src/controllers/${controller}`);
    console.log(`✅ ${controller}.js loaded successfully`);
  } catch (error) {
    console.log(`❌ ${controller}.js failed:`, error.message);
  }
});

// Test routes
console.log('\n🛣️ Testing Routes:');
const routes = [
  'auth',
  'customer',
  'driver',
  'admin',
  'public'
];

routes.forEach(route => {
  try {
    require(`./src/routes/${route}`);
    console.log(`✅ ${route}.js loaded successfully`);
  } catch (error) {
    console.log(`❌ ${route}.js failed:`, error.message);
  }
});

// Test main app
console.log('\n🏗️ Testing Main Application:');
try {
  const app = require('./src/app');
  console.log('✅ Express app loaded successfully');
  console.log('✅ All middleware and routes configured');
} catch (error) {
  console.log('❌ Express app failed:', error.message);
}

// Test file structure
console.log('\n📁 Verifying File Structure:');
const requiredDirs = [
  'src',
  'src/config',
  'src/controllers',
  'src/middleware',
  'src/models',
  'src/routes',
  'src/utils',
  'src/seeders',
  'uploads',
  'logs'
];

requiredDirs.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (fs.existsSync(dirPath)) {
    console.log(`✅ ${dir}/ exists`);
  } else {
    console.log(`❌ ${dir}/ missing`);
  }
});

// Test package.json scripts
console.log('\n📦 Package.json Scripts:');
const packageJson = require('./package.json');
const scripts = packageJson.scripts;
Object.keys(scripts).forEach(script => {
  console.log(`✅ ${script}: ${scripts[script]}`);
});

console.log('\n🎉 Backend Structure Test Complete!');
console.log('\n📋 Next Steps:');
console.log('1. Install and start MongoDB');
console.log('2. Run: npm run seed:dev');
console.log('3. Run: npm run dev');
console.log('4. Test API endpoints at http://localhost:5000/api');
console.log('5. Check health endpoint: http://localhost:5000/health');

console.log('\n📚 API Documentation:');
console.log('- Health Check: GET /health');
console.log('- API Info: GET /api');
console.log('- Auth: POST /api/auth/register, /api/auth/login');
console.log('- Public: GET /api/public/home, /api/public/vehicle-types');
console.log('- Customer: GET /api/customer/dashboard (requires auth)');
console.log('- Driver: GET /api/driver/dashboard (requires auth)');
console.log('- Admin: GET /api/admin/dashboard (requires auth)');

console.log('\n🔐 Default Admin Credentials (after seeding):');
console.log('Email: <EMAIL>');
console.log('Password: Admin@123456');

console.log('\n💡 Development Tips:');
console.log('- Use Postman or similar tool to test API endpoints');
console.log('- Check logs/ directory for application logs');
console.log('- Use npm run lint to check code quality');
console.log('- Environment variables are in .env file');
