# SendMe Logistics Backend API

A comprehensive multi-role logistics and delivery booking platform backend built with Node.js, Express, and MongoDB.

## Features

### Core Functionality
- **Multi-role Authentication**: Customer, Driver, and Admin roles with JWT-based authentication
- **Real-time Booking System**: Live tracking and status updates using Socket.IO
- **Dynamic Pricing**: Distance-based fare calculation with peak hour multipliers
- **Payment Integration**: Stripe payment processing with multiple payment methods
- **Notification System**: Email, SMS, and push notifications
- **Review & Rating System**: Comprehensive review system for drivers and customers
- **Promo Code Management**: Flexible discount and promotional code system
- **Support Ticketing**: Built-in customer support system
- **CMS Integration**: Dynamic content management for app content

### Technical Features
- **RESTful API**: Well-structured REST endpoints with proper HTTP status codes
- **Real-time Communication**: Socket.IO for live updates and chat
- **Rate Limiting**: Comprehensive rate limiting for security
- **Input Validation**: Joi-based request validation
- **Error Handling**: Centralized error handling with detailed logging
- **File Upload**: Cloudinary integration for image and document uploads
- **Geolocation Services**: Google Maps integration for distance and routing
- **Database Optimization**: Indexed MongoDB collections for performance
- **Security**: Helmet, CORS, input sanitization, and XSS protection

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Real-time**: Socket.IO
- **Validation**: Joi
- **File Upload**: Multer + Cloudinary
- **Email**: Nodemailer
- **SMS**: Twilio
- **Push Notifications**: Firebase Admin SDK
- **Payments**: Stripe
- **Maps**: Google Maps API
- **Logging**: Winston
- **Testing**: Jest + Supertest

## Project Structure

```
backend/
├── src/
│   ├── config/          # Configuration files
│   │   ├── database.js  # MongoDB connection
│   │   └── cloudinary.js # Cloudinary setup
│   ├── controllers/     # Route controllers
│   │   ├── authController.js
│   │   ├── customerController.js
│   │   ├── driverController.js
│   │   ├── adminController.js
│   │   └── publicController.js
│   ├── middleware/      # Custom middleware
│   │   ├── auth.js      # Authentication middleware
│   │   ├── errorHandler.js
│   │   ├── rateLimiter.js
│   │   └── validation.js
│   ├── models/          # Mongoose models
│   │   ├── User.js
│   │   ├── Booking.js
│   │   ├── Vehicle.js
│   │   ├── Review.js
│   │   ├── PromoCode.js
│   │   ├── Notification.js
│   │   ├── SupportTicket.js
│   │   └── CmsContent.js
│   ├── routes/          # API routes
│   │   ├── auth.js
│   │   ├── customer.js
│   │   ├── driver.js
│   │   ├── admin.js
│   │   └── public.js
│   ├── utils/           # Utility functions
│   │   ├── jwt.js
│   │   ├── logger.js
│   │   ├── email.js
│   │   ├── sms.js
│   │   ├── pricing.js
│   │   └── distance.js
│   ├── seeders/         # Database seeders
│   │   ├── index.js
│   │   ├── vehicleTypes.js
│   │   └── adminUser.js
│   └── app.js           # Express app setup
├── uploads/             # Local file uploads
├── logs/                # Application logs
├── server.js            # Server entry point
├── package.json
└── README.md
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sendme-logistics/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file with your configuration values.

4. **Database Setup**
   - Install MongoDB locally or use MongoDB Atlas
   - Update `MONGODB_URI` in `.env` file

5. **Seed the database**
   ```bash
   npm run seed:dev
   ```

## Configuration

### Required Environment Variables

```env
# Core Configuration
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/sendme-logistics

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_PHONE_NUMBER=your-phone-number

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-key
```

### Optional Services

- **Cloudinary**: For file uploads
- **Firebase**: For push notifications
- **Redis**: For caching (optional)
- **Sentry**: For error monitoring

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Database Operations
```bash
# Seed basic data
npm run seed

# Seed development data (includes sample users)
npm run seed:dev

# Clear all data
npm run seed:clear
```

### Testing
```bash
npm test
```

### Linting
```bash
npm run lint
npm run lint:fix
```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/otp-request` - Request OTP
- `POST /api/auth/otp-verify` - Verify OTP
- `POST /api/auth/refresh-token` - Refresh access token
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Customer Endpoints
- `GET /api/customer/dashboard` - Customer dashboard
- `POST /api/customer/bookings` - Create booking
- `GET /api/customer/bookings/:id` - Get booking details
- `GET /api/customer/bookings-history` - Booking history
- `GET /api/customer/live-tracking/:id` - Live tracking
- `POST /api/customer/reviews` - Submit review

### Driver Endpoints
- `POST /api/driver/register` - Driver registration
- `GET /api/driver/dashboard` - Driver dashboard
- `PUT /api/driver/status` - Toggle online/offline
- `GET /api/driver/job-requests` - Available jobs
- `PUT /api/driver/job-requests/:id/accept` - Accept job
- `PUT /api/driver/trips/:id/status` - Update trip status
- `GET /api/driver/earnings` - Earnings data

### Admin Endpoints
- `GET /api/admin/dashboard` - Admin dashboard
- `GET /api/admin/users` - User management
- `PUT /api/admin/users/:id/status` - Update user status
- `GET /api/admin/bookings` - Booking management
- `POST /api/admin/promo-codes` - Create promo codes
- `GET /api/admin/analytics` - Platform analytics

### Public Endpoints
- `GET /api/public/home` - Home page data
- `GET /api/public/vehicle-types` - Available vehicles
- `GET /api/public/service-areas` - Service coverage
- `GET /api/public/faqs` - FAQ data

## Real-time Features

The application uses Socket.IO for real-time communication:

### Events
- `booking-update` - Booking status changes
- `driver-location` - Driver location updates
- `new-message` - Chat messages
- `driver-status-change` - Driver online/offline status

### Rooms
- `user-{userId}` - User-specific notifications
- `booking-{bookingId}` - Booking-specific updates
- `admin-dashboard` - Admin real-time data

## Security Features

- **Authentication**: JWT-based with refresh tokens
- **Rate Limiting**: Multiple rate limiters for different endpoints
- **Input Validation**: Joi schema validation
- **Data Sanitization**: MongoDB injection and XSS protection
- **CORS**: Configurable cross-origin resource sharing
- **Helmet**: Security headers
- **Password Hashing**: bcrypt with salt rounds

## Monitoring and Logging

- **Winston Logger**: Structured logging with different levels
- **Request Logging**: Morgan middleware for HTTP requests
- **Error Tracking**: Centralized error handling
- **Health Checks**: `/health` endpoint for monitoring

## Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production database
3. Set up external services (email, SMS, payments)
4. Configure reverse proxy (nginx)
5. Set up SSL certificates

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run linting and tests
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [API Docs](http://localhost:5000/api)
- Issues: GitHub Issues
